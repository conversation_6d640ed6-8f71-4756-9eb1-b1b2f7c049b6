import type { Locale } from '@bika/contents/i18n';
import { getDictionary, type Dictionary } from '@bika/contents/i18n/translate';
import { GlobalProvider } from '@bika/domains/website/client/context/global/provider';
import { NextUIFrameworkProvider } from '@bika/ui/framework/next-framework-provier';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { headers } from 'next/headers';
import type React from 'react';
import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';
import '@bika/domains/shared/client/styles/globals.css';
import '@bika/domains/shared/client/styles/markdown.css';
import { APICallerProvider } from '@bika/api-caller/context';
import { LocaleProvider } from '@bika/contents/i18n/context';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // const dictionary = await getDictionary('zh-CN' as Locale);

  // 服务端渲染，无需useMemo，只执行一次
  const httpHeaders = await headers();
  const defaultLang = 'zh-CN';
  const locale: Locale = (getLocaleByHeaders(httpHeaders) || defaultLang) as Locale;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <html lang={defaultLang}>
      <body className={`${inter.className}`}>
        <NextUIFrameworkProvider
          mode={'RSC'}
          initServerData={{
            hostname: process.env.APP_HOSTNAME!,
            storageHostname: process.env.STORAGE_PUBLIC_URL!,
            headers: httpHeaders,
          }}
        >
          <LocaleProvider defaultLocale={locale} defaultDictionary={dictionary}>
            <APICallerProvider>
              {/* 编辑器默认中文版 */}
              <GlobalProvider
                initData={{
                  hostname: '/',
                  servers: {
                    storagePublicUrl: process.env.STORAGE_PUBLIC_URL!,
                    docServerUrl: '',
                    formAppAIBaseUrl: process.env.FORMAPP_AI_BASE_URL,
                    toolSDKAIBaseUrl: process.env.TOOLSDK_AI_BASE_URL,
                    kkFilePreviewUrl: process.env.KK_FILE_PREVIEW_URL,
                  },
                  auth: null,
                  locale: locale as Locale,
                  timeZone: undefined,
                  themeMode: 'system',
                  themeStyle: 'default',
                  isFromCNHost: false,
                  isFromCN: false,
                  headers: httpHeaders,
                  // 这些ENV是前端需要使用的，所以需要传递给前端
                  // 在客户端组件中是不可以直接使用process的
                  env: {},
                  appEnv: 'LOCAL',
                  version: 'editor',
                }}
              >
                {children}
              </GlobalProvider>
            </APICallerProvider>
          </LocaleProvider>
        </NextUIFrameworkProvider>
      </body>
    </html>
  );
}
