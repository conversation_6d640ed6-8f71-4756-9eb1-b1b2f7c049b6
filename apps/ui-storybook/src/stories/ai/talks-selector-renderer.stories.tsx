import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { TalksSelectorRenderer } from '@bika/domains/ai/client/launcher/space-home/talks-selector/talks-selector-renderer';
import type { TalkDetailVO } from '@bika/types/space/vo';
import type { AgentListProps } from '@bika/domains/ai/client/launcher/space-home/agents-selector/agents-selector';

// Mock data for TalkDetailVO
const mockTalkDetailVOs: TalkDetailVO[] = [
  // Expert talk - Agent Builder
  {
    id: 'expert-builder',
    type: 'expert',
    expertKey: 'builder',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    isPinned: false,
    skillsets: [
      {
        kind: 'preset',
        key: 'agent-creation',
        name: 'Agent Creation',
        description: 'Create and configure AI agents',
        skills: [
          {
            key: 'create-agent',
            name: 'Create Agent',
            description: 'Create a new AI agent',
          },
        ],
      },
    ],
  },
  // Expert talk - Space Supervisor
  {
    id: 'expert-supervisor',
    type: 'expert',
    expert<PERSON>ey: 'supervisor',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    isPinned: true,
    skillsets: [
      {
        kind: 'preset',
        key: 'space-management',
        name: 'Space Management',
        description: 'Manage space resources and members',
        skills: [
          {
            key: 'manage-space',
            name: 'Manage Space',
            description: 'Manage space settings and resources',
          },
        ],
      },
    ],
  },
  // Expert talk - Customer Support
  {
    id: 'expert-customer-support',
    type: 'expert',
    expertKey: 'supervisor',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    isPinned: false,
    skillsets: [
      {
        kind: 'preset',
        key: 'customer-support',
        name: 'Customer Support',
        description: 'Handle customer inquiries and support tickets',
        skills: [
          {
            key: 'answer-questions',
            name: 'Answer Questions',
            description: 'Provide answers to customer questions',
          },
          {
            key: 'escalate-issues',
            name: 'Escalate Issues',
            description: 'Escalate complex issues to human agents',
          },
        ],
      },
    ],
  },
  // Expert talk - Report
  {
    id: 'expert-report',
    type: 'expert',
    expertKey: 'report',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    isPinned: false,
  },
  // Expert talk - Mission
  {
    id: 'expert-mission',
    type: 'expert',
    expertKey: 'mission',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
    isPinned: false,
    skillsets: [
      {
        kind: 'preset',
        key: 'team-collaboration',
        name: 'Team Collaboration',
        description: 'Collaborate with team members',
        skills: [
          {
            key: 'communicate',
            name: 'Communicate',
            description: 'Communicate with team members',
          },
        ],
      },
    ],
  },
];

// Default props for the component
const defaultProps: AgentListProps = {
  selected: undefined,
  onSelectedChange: (agent: TalkDetailVO) => {
    console.log('Selected agent:', agent);
  },
  onSubmit: (agent: TalkDetailVO) => {
    console.log('Submitted agent:', agent);
  },
};

const meta = {
  title: '@bika/ai/TalksSelectorRenderer',
  component: TalksSelectorRenderer,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  args: {
    ...defaultProps,
    value: mockTalkDetailVOs,
  },
} satisfies Meta<typeof TalksSelectorRenderer>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithSelectedAgent: Story = {
  args: {
    selected: mockTalkDetailVOs[0],
  },
};

export const WithPinnedAgent: Story = {
  args: {
    selected: mockTalkDetailVOs[1], // The supervisor agent which is pinned
  },
};

export const FewAgents: Story = {
  args: {
    value: mockTalkDetailVOs.slice(0, 2),
  },
};

export const SingleAgent: Story = {
  args: {
    value: [mockTalkDetailVOs[0]],
  },
};

export const ManyAgents: Story = {
  args: {
    value: [
      ...mockTalkDetailVOs,
      // Add more agents to test scrolling
      {
        id: 'extra-agent-1',
        type: 'expert',
        expertKey: 'mission',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isPinned: false,
      },
      {
        id: 'extra-agent-2',
        type: 'expert',
        expertKey: 'report',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isPinned: false,
      },
      {
        id: 'extra-agent-3',
        type: 'expert',
        expertKey: 'builder',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
        isPinned: false,
      },
    ],
  },
};

export const EmptyList: Story = {
  args: {
    value: [],
  },
};
