<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Drag and Drop Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 720px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.2s ease-in-out;
            background: #fafafa;
        }
        
        .drop-zone.drag-over {
            border-color: #007bff;
            background: rgba(0, 123, 255, 0.1);
            transform: scale(1.02);
        }
        
        .drop-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 123, 255, 0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        
        .drop-message {
            background: white;
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 16px 24px;
            color: #007bff;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .file-list {
            margin-top: 20px;
        }
        
        .file-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 4px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .file-size {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AI Chat Input Drag & Drop Test</h1>
        <p>This test simulates the drag-and-drop functionality implemented in the AIChatInput component.</p>
        
        <div class="drop-zone" id="dropZone">
            <div id="dropOverlay" class="drop-overlay" style="display: none;">
                <div class="drop-message">
                    📎 Drop files here
                </div>
            </div>
            
            <div id="defaultContent">
                <h3>Drop files here or click to select</h3>
                <p>Supported formats: Images, PDFs, Documents</p>
                <input type="file" id="fileInput" multiple accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z,.wps,.et,.dps" style="display: none;">
                <button onclick="document.getElementById('fileInput').click()">Select Files</button>
            </div>
        </div>
        
        <div class="status" id="status">Ready to receive files</div>
        
        <div class="file-list" id="fileList"></div>
    </div>

    <script>
        const dropZone = document.getElementById('dropZone');
        const dropOverlay = document.getElementById('dropOverlay');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const status = document.getElementById('status');
        
        let dragCounter = 0;
        
        // Drag and drop event handlers
        function handleDragEnter(e) {
            e.preventDefault();
            e.stopPropagation();
            dragCounter++;
            
            if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
                dropZone.classList.add('drag-over');
                dropOverlay.style.display = 'flex';
                status.textContent = 'Drop files to upload';
                status.className = 'status';
            }
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            dragCounter--;
            
            if (dragCounter === 0) {
                dropZone.classList.remove('drag-over');
                dropOverlay.style.display = 'none';
                status.textContent = 'Ready to receive files';
                status.className = 'status';
            }
        }
        
        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            
            dropZone.classList.remove('drag-over');
            dropOverlay.style.display = 'none';
            dragCounter = 0;
            
            const files = e.dataTransfer.files;
            if (files && files.length > 0) {
                handleFiles(files);
            }
        }
        
        function handleFiles(files) {
            const acceptedTypes = 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.7z,.wps,.et,.dps'.split(',').map(type => type.trim());
            const validFiles = [];
            const invalidFiles = [];
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const isValidType = acceptedTypes.some(acceptType => {
                    if (acceptType.startsWith('.')) {
                        return file.name.toLowerCase().endsWith(acceptType.toLowerCase());
                    } else {
                        return file.type.match(acceptType.replace('*', '.*'));
                    }
                });
                
                if (isValidType) {
                    validFiles.push(file);
                } else {
                    invalidFiles.push(file);
                }
            }
            
            if (validFiles.length > 0) {
                displayFiles(validFiles);
                status.textContent = `Successfully processed ${validFiles.length} file(s)`;
                status.className = 'status success';
            }
            
            if (invalidFiles.length > 0) {
                status.textContent += ` (${invalidFiles.length} file(s) rejected - invalid type)`;
                status.className = 'status error';
            }
        }
        
        function displayFiles(files) {
            fileList.innerHTML = '';
            
            for (const file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';
                
                const fileName = document.createElement('span');
                fileName.textContent = file.name;
                
                const fileSize = document.createElement('span');
                fileSize.className = 'file-size';
                fileSize.textContent = `(${(file.size / 1024).toFixed(1)} KB)`;
                
                fileInfo.appendChild(fileName);
                fileInfo.appendChild(fileSize);
                
                const fileType = document.createElement('span');
                fileType.textContent = file.type || 'Unknown';
                fileType.style.fontSize = '0.8em';
                fileType.style.color = '#6c757d';
                
                fileItem.appendChild(fileInfo);
                fileItem.appendChild(fileType);
                
                fileList.appendChild(fileItem);
            }
        }
        
        // Add event listeners
        dropZone.addEventListener('dragenter', handleDragEnter);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('drop', handleDrop);
        
        // File input change handler
        fileInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files.length > 0) {
                handleFiles(e.target.files);
            }
        });
        
        // Keyboard support (Ctrl/Cmd + U)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                fileInput.click();
            }
        });
    </script>
</body>
</html>
