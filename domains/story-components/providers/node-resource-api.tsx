/* eslint-disable max-lines */
import type { AIWriter } from '@bika/types/ai/bo';
import type {
  AutomationTriggerUpdateDTO,
  AutomationTriggerCreateDTO,
  AutomationActionUpdateDTO,
  AutomationActionCreateDTO,
} from '@bika/types/automation/dto';
import type { WidgetBO } from '@bika/types/dashboard/bo';
import type { WidgetCreateDTO, WidgetDeleteDTO, WidgetUpdateDTO } from '@bika/types/dashboard/dto';
import { DatabaseFieldWithId } from '@bika/types/database/bo';
import type {
  RecordListDTO,
  FieldUpdateDTO,
  FieldCreateDTO,
  ViewUpdateDTO,
  DatabaseViewCreateDTO,
  FieldGetDTO,
} from '@bika/types/database/dto';
import type { ViewVO } from '@bika/types/database/vo';
import type { IResourceEditorUIHandler } from '@bika/types/editor/context';
import type { EditorNodeFolderDTO } from '@bika/types/editor/dto';
import { EmailBoxesDTO } from '@bika/types/email/dto';
import type { SpaceIntegrationType } from '@bika/types/integration/bo';
import type { IntegrationListDTOWithoutSpaceID } from '@bika/types/integration/dto';
import type { IntegrationVO } from '@bika/types/integration/vo';
import type { NodeResource } from '@bika/types/node/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import type {
  NodeCreateDTOWithoutSpaceId,
  NodeListDTOWithoutSpaceID,
  NodeUpdateDTOReqWithoutSpaceId,
  NodeMoveDTOReqWithoutSpaceId,
} from '@bika/types/node/dto';
import type { NodeDetailVO, NodeTreeVO } from '@bika/types/node/vo';
import { SkillsetSelectBO } from '@bika/types/skill/bo';
import { SkillsetSearchDTO } from '@bika/types/skill/dto';
import { SkillsetVO, SkillsetVOPagination } from '@bika/types/skill/vo';
import type { SpaceUIModal } from '@bika/types/space/bo';
import type { StoreTemplateVO } from '@bika/types/template/vo';
import type { UnitSearch } from '@bika/types/unit/dto';
import type { MemberVO, RoleVO, TeamVO } from '@bika/types/unit/vo';
import type { GlobalModalConfig } from '@bika/types/website/bo';
import type { CommitSurveyDTO } from '@bika/types/website/dto';

export const teams: TeamVO[] = [
  {
    id: 'team1',
    name: 'Team 1',
    type: 'Team',
    isGuest: false,
  },
  {
    id: 'team2',
    name: 'Team 2',
    type: 'Team',
    isGuest: false,
  },
  {
    id: 'team3',
    name: 'Team 3',
    type: 'Team',
    isGuest: false,
  },
];

export const roles: RoleVO[] = [
  {
    id: 'role-1',
    name: 'Admin',
    manageSpace: true,
    type: 'Role',
  },
  {
    id: 'role-2',
    name: 'User',
    manageSpace: true,
    type: 'Role',
  },
  {
    id: 'role-3',
    name: 'Guest',
    manageSpace: true,
    type: 'Role',
  },
];

export const helpProviderMembers: MemberVO[] = [
  {
    id: '1',
    userId: '',
    name: 'Kelly',
    email: '<EMAIL>',
    type: 'Member',
    avatar: {
      type: 'EMOJI',
      emoji: '👨',
    },
    relationType: 'USER',
    isGuest: false,
    isSpaceOwner: true,
    teams: [{ id: 'team1', path: 'team1', type: 'Team', name: 'team1', isGuest: false }],
  },
  {
    id: '2',
    userId: '',
    name: 'Marry',
    email: '<EMAIL>',
    type: 'Member',
    avatar: {
      type: 'EMOJI',
      emoji: '👨',
    },
    relationType: 'USER',
    isGuest: false,
    isSpaceOwner: true,
    teams: [{ id: 'team2', path: 'team2', type: 'Team', name: 'team2', isGuest: false }],
  },
  {
    id: '3',
    userId: '',
    name: 'Tom',
    email: '<EMAIL>',
    type: 'Member',
    avatar: {
      type: 'EMOJI',
      emoji: '👨',
    },
    relationType: 'USER',
    isGuest: false,
    isSpaceOwner: true,
    teams: [{ id: 'team3', path: 'team3', type: 'Team', name: 'team3', isGuest: false }],
  },
];

export function helpOnClick() {
  // eslint-disable-next-line no-alert
  console.log('This is a demo please login');
  return (() => {}) as any;
}
export function helpOnLoad() {
  // eslint-disable-next-line no-alert
  console.log('This is a demo please login');
  return null!;
}

export const helpProviderUIHandler: IResourceEditorUIHandler = {
  onClickPreview: () => {},
  onClickNewView: async () => {},
  onClickNewField: async () => {},
  onClickField: async () => {},
  onClickDeleteField: async () => {},
  onClickView: async () => {},
  onClickDeleteView: async () => {},
  onClickTrigger: async () => {},
  onClickNewTrigger: async () => {},
  onClickDeleteTrigger: async () => {},
  onClickAction: async () => {},
  onClickNewAction: async () => {},
  onClickDeleteAction: async () => {},

  onSelectNewIntegration(_integrationType: SpaceIntegrationType): void {
    return helpOnClick();
  },
  showUIModal(_modal: GlobalModalConfig | SpaceUIModal | null, _context: 'SPACE' | 'GLOBAL'): void {
    return helpOnLoad();
  },
};
export const helpProviderAPI: INodeResourceApi = {
  unit: {
    useRoles: () => ({
      data: {
        data: roles,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 3,
        },
      },
      isLoading: false,
      refetch: () => {},
    }),
    useTeams: () => ({
      data: teams,
      isLoading: false,
      refetch: () => {},
    }),
    getTeams: async (teamId?: string | undefined) => {
      const data: TeamVO[] = [];
      switch (teamId) {
        case 'team1':
          data.push({ type: 'Team', id: 'team1-1', name: 'Team 1-1', isGuest: false });
          data.push({ type: 'Team', id: 'team1-2', name: 'Team 1-2', isGuest: false });
          break;
        case 'team2':
          data.push({ type: 'Team', id: 'team2-1', name: 'Team 2-1', isGuest: false });
          data.push({
            id: 'team2-2',
            name: 'Team 2-2',
            type: 'Team',
            isGuest: false,
          });
          break;
        case 'team3':
          data.push({
            id: 'team3-1',
            name: 'Team 3-1',
            type: 'Team',
            isGuest: false,
          });
          data.push({
            id: 'team3-2',
            name: 'Team 3-2',
            type: 'Team',
            isGuest: false,
          });
          break;
        default:
          break;
      }
      return Promise.resolve(data);
    },
    getTeamSubList: async (teamId: string) => {
      const data: TeamVO[] = [];
      switch (teamId) {
        case 'team1':
          data.push({ type: 'Team', id: 'sub-team1-1', name: 'Team 1-1', isGuest: false });
          data.push({ type: 'Team', id: 'sub-team1-2', name: 'Team 1-2', isGuest: false });
          break;
        case 'team2':
          data.push({ type: 'Team', id: 'sub-team2-1', name: 'Team 2-1', isGuest: false });
          data.push({
            id: 'sub-team2-2',
            name: 'Team 2-2',
            type: 'Team',
            isGuest: false,
          });
          break;
        case 'team3':
          data.push({
            id: 'sub-team3-1',
            name: 'Team 3-1',
            type: 'Team',
            isGuest: false,
          });
          data.push({
            id: 'sub-team3-2',
            name: 'Team 3-2',
            type: 'Team',
            isGuest: false,
          });
          break;
        default:
          break;
      }
      return Promise.resolve({
        data,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 3,
        },
      });
    },
    getSearchUnits: async (search: UnitSearch) => {
      const data: TeamVO[] = [];
      if (search.name === 'team') {
        data.push({
          id: 'team1',
          name: 'Team 1',
          type: 'Team',
          isGuest: false,
        });
        data.push({
          id: 'team2',
          name: 'Team 2',
          type: 'Team',
          isGuest: false,
        });
        data.push({
          id: 'team3',
          name: 'Team 3',
          type: 'Team',
          isGuest: false,
        });
      }
      return Promise.resolve({
        data,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 3,
        },
      });
    },
    useUnitsByIds: (unitIds: string[]) => ({
      data: {
        pagination: {
          pageNo: 1,
          pageSize: unitIds.length,
          total: unitIds.length,
        },
        data: unitIds.map((id) => {
          if (id === 'team1') {
            return {
              id: 'team1',
              name: 'Team 1',
              type: 'Team',
              isGuest: false,
            };
          }
          if (id === 'team2') {
            return {
              id: 'team2',
              name: 'Team 2',
              type: 'Team',
              isGuest: false,
            };
          }
          if (id === 'team3') {
            return {
              id: 'team3',
              name: 'Team 3',
              type: 'Team',
              isGuest: false,
            };
          }
          return {
            id: 'role1',
            name: 'Role 1',
            type: 'Role',
          } as RoleVO;
        }),
      },
      isLoading: false,
      refetch: () => {},
    }),
    useMembers: () => ({
      data: {
        data: helpProviderMembers,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 3,
        },
      },
      isLoading: false,
      refetch: () => {},
    }),
  },

  automation: {
    updateAutomationVariables: (_automationId: string) => {},
    getAutomationGlobalVariables: (_actionId: string | undefined, isList?: boolean) =>
      isList
        ? {
            _actions: {
              find_records: {
                records: '记录列表',
              },
            },
            _triggers: {},
          }
        : {
            _actions: {
              round_robin: {
                database: {
                  id: 'db1',
                  name: 'Database 1',
                },
                record: {
                  cells: {
                    duty_member: {
                      id: 'cell1',
                      name: 'Duty Member',
                      data: 'data1',
                      value: 'value1',
                    },
                  },
                },
              },
            },
            _triggers: {},
          },
    useTrigger: (_triggerId?: string) => ({
      data: undefined,
      isLoading: false,
      refetch: () => {},
    }),
    // getFormAppAIAccountToken: () => ({
    //   data: 'test_token',
    //   isLoading: false,
    //   refetch: () => {},
    // }),
    getToolSDKAIAccountToken: () => ({
      data: 'test_token',
      isLoading: false,
      refetch: () => {},
    }),
    testAction: async () => {},
    testTrigger: async () => {},
    getAutomationTestResult: () => ({
      success: true,
      runTime: new Date().toISOString(),
      actionType: 'FIND_RECORDS',
      output: {
        database: {
          id: 'db1',
          name: 'Database 1',
        },
        record: {
          cells: {
            duty_member: {
              id: 'cell1',
              name: 'Duty Member',
              data: 'data1',
              value: 'value1',
            },
          },
        },
      },
    }),
    useAutomationMutation(): {
      update: () => Promise<void>;
      create: () => Promise<void>;
      updateTrigger: (dto: AutomationTriggerUpdateDTO) => Promise<void>;
      deleteTrigger: (triggerId: string, okCallback?: () => void) => Promise<void>;
      createTrigger: (dto: AutomationTriggerCreateDTO) => Promise<void>;
      updateAction: (dto: AutomationActionUpdateDTO) => Promise<void>;
      deleteAction: (actionId: string, okCallback?: () => void) => Promise<void>;
      createAction: (dto: AutomationActionCreateDTO) => Promise<void>;
      isMutating: boolean;
    } {
      return {
        update: async () => {},
        create: async () => {},
        updateTrigger: async () => {},
        deleteTrigger: async () => {},
        createTrigger: async () => {},
        updateAction: async () => {},
        deleteAction: async () => {},
        createAction: async () => {},

        isMutating: false,
      };
    },
  },
  node: {
    useScope: () => ({
      scope: 'SPACE',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setScope: (_scope: any) => {},
    }),
    uploadFile: (data: { file: File; filePrefix: string }) => Promise.resolve(undefined),
    useRootNode(): NodeTreeVO | undefined {
      return helpOnLoad();
    },
    getSearchNodes: async (dto: NodeListDTOWithoutSpaceID) => {
      const { resourceType } = dto;
      switch (resourceType) {
        case 'DATABASE':
          return Promise.resolve([
            {
              id: 'db1',
              name: 'Database 1',
              type: 'DATABASE',
              sharing: false,
              hasShareLock: false,
              hasPermissions: false,
            },
            {
              id: 'db2',
              name: 'Database 2',
              type: 'DATABASE',
              sharing: false,
              hasShareLock: false,
              hasPermissions: false,
            },
          ]);
        case 'DASHBOARD':
          return Promise.resolve([
            {
              id: 'dashboard1',
              name: 'Dashboard 1',
              type: 'DASHBOARD',
              sharing: false,
              hasShareLock: false,
              hasPermissions: false,
            },
            {
              id: 'dashboard2',
              name: 'Dashboard 2',
              type: 'DASHBOARD',
              sharing: false,
              hasShareLock: false,
              hasPermissions: false,
            },
          ]);
        // case 'VIEW':
        //   return Promise.resolve([
        //     {
        //       id: 'view1',
        //       name: 'View 1',
        //       type: 'VIEW',
        //       sharing: false,
        //       hasShareLock: false,
        //       hasPermissions: false,
        //     },
        //   ]);
        default:
          return Promise.resolve([]);
      }
    },
    useSearchNodes: (dto: NodeListDTOWithoutSpaceID) => {
      const { resourceType } = dto;
      const data: NodeTreeVO[] = [];
      switch (resourceType) {
        case 'DATABASE':
          data.push({
            id: 'db1',
            name: 'Database 1',
            type: 'DATABASE',
            sharing: false,
            hasShareLock: false,
            hasPermissions: false,
          });
          data.push({
            id: 'db2',
            name: 'Database 2',
            type: 'DATABASE',
            sharing: false,
            hasShareLock: false,
            hasPermissions: false,
          });
          break;
        case 'DASHBOARD':
          data.push({
            id: 'dashboard1',
            name: 'Dashboard 1',
            type: 'DASHBOARD',
            sharing: false,
            hasShareLock: false,
            hasPermissions: false,
          });
          data.push({
            id: 'dashboard2',
            name: 'Dashboard 2',
            type: 'DASHBOARD',
            sharing: false,
            hasShareLock: false,
            hasPermissions: false,
          });
          break;
        // case 'VIEW':
        //   data.push({
        //     id: 'view1',
        //     name: 'View 1',
        //     type: 'VIEW',
        //     sharing: false,
        //     hasShareLock: false,
        //     hasPermissions: false,
        //   });
        //   data.push({
        //     id: 'view2',
        //     name: 'View 2',
        //     type: 'VIEW',
        //     sharing: false,
        //     hasShareLock: false,
        //     hasPermissions: false,
        //   });
        //   break;
        default:
          break;
      }
      return {
        data,
        isLoading: false,
        refetch: () => {},
      };
    },
    getNodeResourceDetail(_nodeId: string | undefined): {
      data: NodeDetailVO | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return helpOnLoad();
    },

    getNodeResourceBO: (nodeId: string): Promise<NodeResource> =>
      Promise.resolve({
        name: 'Example Name',
        resourceType: 'DATABASE',
        description: 'Example Description',
        id: nodeId,
        templateId: 'templateId',
        permissions: [],
      }),

    useNodeResourceBO(_nodeId: string | undefined): {
      data: NodeResource | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return helpOnLoad();
    },

    invalidateNodeResourceDetail(): void {
      helpOnClick();
    },

    useNodeResourceBOMutation(): {
      create: (nodeCreateReq: NodeCreateDTOWithoutSpaceId) => Promise<void>;
      delete: (id: string) => void;
      update: (nodeUpdateDTO: NodeUpdateDTOReqWithoutSpaceId) => void;
      move: (nodeUpdateMove: NodeMoveDTOReqWithoutSpaceId) => void;
      isMutating: boolean;
    } {
      return helpOnLoad();
    },
  },
  dashboard: {
    useWidgets: () => ({
      data: [
        {
          id: 'widget1',
          name: 'widget 1',
          type: 'TEXT',
          datasource: {
            type: 'CUSTOM',
            text: 'text',
          },
        },
        {
          id: 'widget2',
          name: 'widget 2',
          type: 'TEXT',
          datasource: {
            type: 'CUSTOM',
            text: 'text',
          },
        },
      ],
      isLoading: false,
      refetch: () => {},
    }),

    getDashboardWidget(_: { widgetId: string; dashboardId?: string }): {
      data: WidgetBO | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return helpOnLoad();
    },

    createWidget: async (_: WidgetCreateDTO) => {},
    updateWidget: async (_: WidgetUpdateDTO) => {},
    deleteWidget: async (_: WidgetDeleteDTO) => {},
  },
  database: {
    // @ts-ignore
    getFields: (databaseId: string) => {
      const columns = [
        { databaseId: databaseId!, primary: false, id: 'col1', name: '单行文本', type: 'SINGLE_TEXT' },
        { databaseId: databaseId!, primary: false, id: 'col2', name: '多行文本', type: 'LONG_TEXT' },
        { databaseId: databaseId!, primary: false, id: 'col3', name: '复选框', type: 'CHECKBOX' },
        {
          databaseId: databaseId!,
          primary: false,
          id: 'own_link',
          name: 'TABLE_1',
          type: 'ONE_WAY_LINK',
          property: {},
        },

        { databaseId: databaseId!, primary: false, id: 'link', name: 'TABLE_2', type: 'LINK', property: {} },
      ];

      return {
        data: columns,
      };
    },

    // @ts-ignore
    getLinkDatabase: (databaseId1: string | undefined) => {
      const databaseId = 'database1';

      const data1 = {
        id: databaseId!,
        name: 'database',
        spaceId: 'spaceId', // Add the missing property
        views: [
          {
            property: {},
            id: 'view1',
            name: '视图1',
            type: 'FORM',
            databaseId: databaseId!,
            columns: [
              { databaseId: databaseId!, primary: false, id: 'col1', name: '单行文本', type: 'SINGLE_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col2', name: '多行文本', type: 'LONG_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col3', name: '复选框', type: 'CHECKBOX' },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col4',
                name: '货币',
                type: 'CURRENCY',
                property: {
                  precision: 2,
                  commaStyle: 'thousand',
                  symbol: '$',
                  symbolAlign: 'left',
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col5',
                name: '日期时间',
                type: 'DATETIME',
                property: {
                  dateFormat: 'YYYY-MM-DD',
                  includeTime: false,
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col6',
                name: '关联',
                type: 'LINK',
                property: {
                  foreignDatabaseId: 'db1',
                  brotherFieldId: 'col1',
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col7',
                name: '多选',
                type: 'MULTI_SELECT',
                property: {
                  options: [
                    { id: 'option1', name: '选项1' },
                    { id: 'option2', name: '选项2' },
                  ],
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col8',
                name: '数字',
                type: 'NUMBER',
                property: { precision: 2 },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col9',
                name: '单选',
                type: 'SINGLE_SELECT',
                property: {
                  options: [
                    { id: 'option1', name: '选项1' },
                    { id: 'option2', name: '选项2' },
                  ],
                },
              },
            ],
          },
          {
            property: {},
            id: 'view2',
            name: 'View 2',
            type: 'TABLE',
            databaseId: databaseId!,
            columns: [
              { databaseId: databaseId!, primary: false, id: 'col1', name: '列1', type: 'SINGLE_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col2', name: '列2', type: 'SINGLE_TEXT' },
            ],
          },
        ],
      };

      return {
        isFetching: false,
        isLoading: false,
        data: {
          databaseId1: data1,
        },
      };
    },

    getDatabaseVO: (databaseId: string | undefined) => ({
      isFetching: false,
      data: {
        id: databaseId!,
        name: 'database',
        spaceId: 'spaceId', // Add the missing property
        views: [
          {
            property: {},
            id: 'view1',
            name: '视图1',
            type: 'FORM',
            databaseId: databaseId!,
            columns: [
              { databaseId: databaseId!, primary: false, id: 'col1', name: '单行文本', type: 'SINGLE_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col2', name: '多行文本', type: 'LONG_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col3', name: '复选框', type: 'CHECKBOX' },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col4',
                name: '货币',
                type: 'CURRENCY',
                property: {
                  precision: 2,
                  commaStyle: 'thousand',
                  symbol: '$',
                  symbolAlign: 'left',
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col5',
                name: '日期时间',
                type: 'DATETIME',
                property: {
                  dateFormat: 'YYYY-MM-DD',
                  includeTime: false,
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col6',
                name: '关联',
                type: 'LINK',
                property: {
                  foreignDatabaseId: 'db1',
                  brotherFieldId: 'col1',
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col7',
                name: '多选',
                type: 'MULTI_SELECT',
                property: {
                  options: [
                    { id: 'option1', name: '选项1' },
                    { id: 'option2', name: '选项2' },
                  ],
                },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col8',
                name: '数字',
                type: 'NUMBER',
                property: { precision: 2 },
              },
              {
                databaseId: databaseId!,
                primary: false,
                id: 'col9',
                name: '单选',
                type: 'SINGLE_SELECT',
                property: {
                  options: [
                    { id: 'option1', name: '选项1' },
                    { id: 'option2', name: '选项2' },
                  ],
                },
              },
            ],
          },
          {
            property: {},
            id: 'view2',
            name: 'View 2',
            type: 'TABLE',
            databaseId: databaseId!,
            columns: [
              { databaseId: databaseId!, primary: false, id: 'col1', name: '列1', type: 'SINGLE_TEXT' },
              { databaseId: databaseId!, primary: false, id: 'col2', name: '列2', type: 'SINGLE_TEXT' },
            ],
          },
        ],
      },
      isLoading: false,
      refetch: () => {},
    }),
    getFieldsBO: (_databaseId: string | undefined) => ({
      data: [],
      isLoading: false,
      refetch: () => {},
    }),
    getFieldsRO: (_databaseId: string | undefined) => ({
      data: [],
      isLoading: false,
      isFetching: false,
      refetch: () => {},
    }),
    getRecordVO: (_databaseId: string | undefined, recordId: string | undefined) => ({
      data: {
        fields: [
          {
            id: 'fldat3YwYASYkznp0XLzQHTy',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '单行文本',
            description: '',
            type: 'SINGLE_TEXT',
            primary: true,
            privilege: 'TYPE_EDIT',
          },
          {
            id: 'fldklwfHzA51PfMbMDlvAb2M',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '多选',
            description: '',
            type: 'MULTI_SELECT',
            property: {
              options: [
                {
                  name: 'Option 1',
                },
                {
                  name: 'Option 2',
                },
              ],
              defaultValue: [],
            },
            primary: false,
            privilege: 'FULL_EDIT',
          },
          {
            id: 'fldAUcrPR9kY66pozcTf4osQ',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '附件',
            description: '',
            type: 'ATTACHMENT',
            primary: false,
            privilege: 'FULL_EDIT',
          },
        ],
        revision: 1,
        record: {
          id: 'recZzSfOIukHW0C4HUkU3Z2W',
          databaseId: 'datJ047gtrKV85JBVy3I7rbG',
          revision: 1,
          cells: {
            fldat3YwYASYkznp0XLzQHTy: {
              id: 'fldat3YwYASYkznp0XLzQHTy',
              name: '单行文本',
              data: '1',
              value: '1',
            },
            fldklwfHzA51PfMbMDlvAb2M: {
              id: 'fldklwfHzA51PfMbMDlvAb2M',
              name: '多选',
              data: null,
              value: null,
            },
            fldAUcrPR9kY66pozcTf4osQ: {
              id: 'fldAUcrPR9kY66pozcTf4osQ',
              name: '附件',
              data: null,
              value: null,
            },
          },
        },
      },
      isLoading: false,
      refetch: () => {},
    }),
    getRecord: (_databaseId: string, _recordId: string) =>
      Promise.resolve({
        fields: [
          {
            id: 'fldat3YwYASYkznp0XLzQHTy',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '单行文本',
            description: '',
            type: 'SINGLE_TEXT',
            primary: true,
            privilege: 'TYPE_EDIT',
          },
          {
            id: 'fldklwfHzA51PfMbMDlvAb2M',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '多选',
            description: '',
            type: 'MULTI_SELECT',
            property: {
              options: [
                {
                  name: 'Option 1',
                },
                {
                  name: 'Option 2',
                },
              ],
              defaultValue: [],
            },
            primary: false,
            privilege: 'FULL_EDIT',
          },
          {
            id: 'fldAUcrPR9kY66pozcTf4osQ',
            databaseId: 'datJ047gtrKV85JBVy3I7rbG',
            name: '附件',
            description: '',
            type: 'ATTACHMENT',
            primary: false,
            privilege: 'FULL_EDIT',
          },
        ],
        revision: 1,
        record: {
          id: 'recZzSfOIukHW0C4HUkU3Z2W',
          databaseId: 'datJ047gtrKV85JBVy3I7rbG',
          revision: 1,
          cells: {
            fldat3YwYASYkznp0XLzQHTy: {
              id: 'fldat3YwYASYkznp0XLzQHTy',
              name: '单行文本',
              data: '1',
              value: '1',
            },
            fldklwfHzA51PfMbMDlvAb2M: {
              id: 'fldklwfHzA51PfMbMDlvAb2M',
              name: '多选',
              data: null,
              value: null,
            },
            fldAUcrPR9kY66pozcTf4osQ: {
              id: 'fldAUcrPR9kY66pozcTf4osQ',
              name: '附件',
              data: null,
              value: null,
            },
          },
        },
      }),
    getRecordsVo: (_dto: RecordListDTO) => ({
      data: {
        rows: [
          {
            id: 'record1',
            databaseId: 'db1',
            revision: 1,
            cells: {
              col1: {
                id: 'cell1',
                name: 'cell1',
                data: 'data1',
                value: 'value1',
              },
              col2: {
                id: 'cell2',
                name: 'cell2',
                data: 'data2',
                value: 'value2',
              },
            },
          },
          {
            id: 'record2',
            databaseId: 'db1',
            revision: 2,
            cells: {
              col1: {
                id: 'cell1',
                name: 'cell1',
                data: 'data1',
                value: 'value1',
              },
              col2: {
                id: 'cell2',
                name: 'cell2',
                data: 'data2',
                value: 'value2',
              },
            },
          },
        ],
        total: 0,
      },
      isLoading: false,
      refetch: () => {},
    }),

    useDatabaseView: (_: { viewId: string; databaseId?: string } | undefined) => ({
      data: {
        id: 'viwLWOlzfDKMUhhVIjwEywCi',
        name: 'Default',
        description: '',
        type: 'TABLE',
        databaseId: 'datnMFSXHc95OarXTWABAX9j',
        preViewId: null,
        filters: {
          conditions: [],
          conjunction: 'And',
        },
        property: {
          autoSave: false,
          hidden: false,
          autoHeadHeight: false,
        },
        columns: [
          {
            id: 'flduoTl4XIPEf7oByYIpGIgo',
            databaseId: 'datnMFSXHc95OarXTWABAX9j',
            name: '單行文本',
            description: '',
            type: 'SINGLE_TEXT',
            primary: true,
            privilege: 'TYPE_EDIT',
          },
          {
            id: 'fldoVSRI9SwbocvxdfKHbqJR',
            databaseId: 'datnMFSXHc95OarXTWABAX9j',
            name: '多選',
            description: '',
            type: 'MULTI_SELECT',
            property: {
              options: [
                {
                  name: 'Option 1',
                },
                {
                  name: 'Option 2',
                },
              ],
              defaultValue: [],
            },
            primary: false,
            privilege: 'FULL_EDIT',
          },
          {
            id: 'fldHxwumED3utT44PFEjCDzx',
            databaseId: 'datnMFSXHc95OarXTWABAX9j',
            name: '附件',
            description: '',
            type: 'ATTACHMENT',
            primary: false,
            privilege: 'FULL_EDIT',
          },
        ],
      },
      isLoading: false,
      refetch: () => {},
    }),
    useDatabaseField(_: FieldGetDTO | undefined): {
      data: DatabaseFieldWithId | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return helpOnLoad();
    },

    useDatabaseMutation(): {
      updateField: (args: FieldUpdateDTO) => Promise<void>;
      deleteField: (args: { databaseId: string; fieldId: string }) => Promise<void>;
      createField: (args: FieldCreateDTO) => Promise<void>;
      updateView: (dto: ViewUpdateDTO) => Promise<void>;
      deleteView: (args: { databaseId: string; viewId: string; okCallback?: () => void }) => Promise<void>;
      createView: (dto: DatabaseViewCreateDTO) => Promise<ViewVO>;
      refetchView: (viewId: string) => void;
      isMutating: boolean;
    } {
      return {
        updateField: async () => {},
        deleteField: async () => {},
        createField: async () => {},
        updateView: async () => {},
        deleteView: async () => {},
        createView: async (dto: DatabaseViewCreateDTO) => ({
          id: 'view1',
          name: dto.name as string,
          type: dto.type,
          databaseId: dto.databaseId,
          columns: [],
        }),
        refetchView: async () => {},

        isMutating: false,
      };
    },
  },

  useIntegrations(dto: IntegrationListDTOWithoutSpaceID): {
    data: IntegrationVO[] | undefined;
    isLoading: boolean;
    refetch: () => void;
  } {
    return {
      data: [],
      isLoading: false,
      refetch: () => {},
    };
  },

  folder: {
    useFolderDetail(folderId: string | undefined): {
      data: EditorNodeFolderDTO | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return helpOnLoad();
    },
    uploadFolderLogo: (data: { file: File; filePrefix: string }) => Promise.resolve(undefined),
  },

  mission: {
    useMissionMutation: () => ({
      create: async () => {},
    }),
  },

  commitSurvey(survey: CommitSurveyDTO): void {
    return helpOnClick();
  },
  template: {
    useTemplateDetail(templateId: string | undefined): {
      data: StoreTemplateVO | undefined;
      isLoading: boolean;
      refetch: () => void;
    } {
      return {
        data: undefined,
        isLoading: false,
        refetch: () => {},
      };
    },
  },
  ai: {
    async searchSkillset(_params?: SkillsetSearchDTO): Promise<SkillsetVOPagination> {
      return {
        pagination: {
          pageNo: 1,
          pageSize: 10,
          total: 10,
        },
        data: [],
      };
    },
    async getSkillset(_skillset: SkillsetSelectBO): Promise<SkillsetVO | undefined> {
      return Promise.resolve(undefined);
    },

    async callAIWriter(writer: AIWriter, userPrompt: string): Promise<{ data: any; value: string }> {
      return {
        data: {},
        value: `ai generation preview: ${Math.random()}`,
      };
    },
  },
  email: {
    fetchBoxes: (_dto: EmailBoxesDTO) => Promise.resolve([]),
  },
};
