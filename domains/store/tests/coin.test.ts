import dayjs from 'dayjs';
import { test, expect, describe, vi } from 'vitest';
import { waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { db } from '@bika/server-orm';
import { MockContext } from '../../__tests__/mock/mock.context';
import { SpaceController } from '../../space/apis';
import { SpaceSO } from '../../space/server/space-so';
import { CoinAccountSO } from '../server/coin-account-so';

test('Test Bika Coin', async () => {
  const user = await MockContext.createUser();

  // 开发环境下，默认有bika coin奖励，100个
  const defaultCoin = BigInt(100);
  await waitForMatchToBeMet(
    async () => {
      const account2 = await user.coins.getAccount();
      return account2.balance === defaultCoin;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  const account = await CoinAccountSO.get('USER', user.id); // 两种获得银行账户的方式
  expect(account.balance).toBe(defaultCoin);

  const n = ***************; // super big, bigger than bigint
  const { updatedAccount: updatedAccount1 } = await account.earn(n, 'CREDIT', {
    reason: 'gm',
    description: 'Test Credit Coin',
  });
  const { updatedAccount: updatedAccount2 } = await account.earn(n, 'CREDIT', {
    reason: 'gm',
    description: 'Test Credit Coin',
  });

  expect(updatedAccount1).toBeDefined();
  expect(updatedAccount2.balance).toBe(defaultCoin + BigInt(n) + BigInt(n));
  expect(updatedAccount2.credit).toBe(defaultCoin + BigInt(n) + BigInt(n));
  expect(updatedAccount2.currency).toBe(BigInt(0));

  const accountAgain = await CoinAccountSO.get('USER', user.id);
  const realBalance = await accountAgain.clearing();
  expect(updatedAccount2.balance).toBe(realBalance.balance);

  // 尝试加真货币
  const { updatedAccount: updatedAccount3 } = await account.earn(n, 'CURRENCY', {
    reason: 'gm',
    description: 'Test Currency Coin',
  });
  expect(updatedAccount3.currency).toBe(BigInt(n));
  expect(updatedAccount3.balance).toBe(defaultCoin + BigInt(n) + BigInt(n) + BigInt(n));

  // 加 virtual 策略
  expect(await accountAgain.virtualBalance()).toBe(accountAgain.balance);
  expect(accountAgain.strategies.length).toBe(0);

  await accountAgain.addStrategyUnique({
    type: 'monthly-credit',
    startDate: dayjs().toISOString(),
    count: 1000,
  });
  await accountAgain.addStrategyUnique({
    type: 'monthly-credit',
    startDate: dayjs().toISOString(),
    count: 2000,
  });
  await accountAgain.addStrategyUnique({
    type: 'monthly-credit',
    startDate: dayjs().toISOString(),
    count: 3000,
  });
  expect(accountAgain.strategies.length).toBe(1);
  expect(accountAgain.strategies[0].count).toBe(3000);
  expect(await accountAgain.virtualBalance()).toBe(accountAgain.balance + BigInt(3000));

  await accountAgain.addStrategyUnique({
    type: 'daily-credit',
    startDate: dayjs().toISOString(),
    count: 300,
  });
  expect(await accountAgain.virtualBalance()).toBe(accountAgain.balance + BigInt(3000) + BigInt(300));

  // 故意过气了，这个是不生效的 invalid
  await accountAgain.addStrategyUnique({
    type: 'daily-credit',
    startDate: dayjs().subtract(2, 'day').toISOString(),
    count: *************,
  });
  expect(await accountAgain.virtualBalance()).toBe(accountAgain.balance + BigInt(3000));

  // 1 小时开始的策略
  await accountAgain.addStrategyUnique({
    type: 'daily-credit',
    startDate: dayjs().subtract(1, 'hour').toISOString(),
    count: 301,
  });
  expect(await accountAgain.virtualBalance()).toBe(accountAgain.balance + BigInt(3000) + BigInt(301));
});

describe('space strategy test', () => {
  test('Test space daily credit Strategy', async () => {
    const now = dayjs('2025-05-31 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    let coinAccount = await space.billing.getCoinsAccount();
    // 消耗6000积分, 今天的积分应该是-2000
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    const credit = await SpaceController.getCredit(space.id);
    expect(credit.virtualBalance).toBe(-2000);
    expect(credit.virtualGiftCredit).toBe(0);
    expect(credit.virtualSubscribeCredit).toBe(-2000);
    expect(credit.credit).toBe(0); // 永久积分
    // 将系统时间向后调整一天, 又赠送了3000, 因此虚拟积分应该是-2000 + 3000 = 1000
    vi.useFakeTimers().setSystemTime(now.add(1, 'day').toDate());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(1000);
    expect(credit2.virtualGiftCredit).toBe(3000);
    expect(credit2.virtualSubscribeCredit).toBe(-2000);
    expect(credit.credit).toBe(0); // 永久积分
    // 今日再次消耗4000积分, 因为还有1000积分, 所以虚拟积分应该是1000 - 4000 = -3000
    coinAccount = await space.billing.getCoinsAccount();
    await coinAccount.redeem(4000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    const credit3 = await SpaceController.getCredit(space.id);
    expect(credit3.virtualBalance).toBe(-3000);
    expect(credit3.virtualGiftCredit).toBe(0);
    expect(credit3.virtualSubscribeCredit).toBe(-3000);
    expect(credit3.credit).toBe(0); // 永久积分
    // 将系统时间向后调整一个月,前一个月使用的积分都会不读取，所以清零了
    vi.useFakeTimers().setSystemTime(now.add(1, 'month').toDate());
    const credit4 = await SpaceController.getCredit(space.id);
    expect(credit4.virtualBalance).toBe(4000);
    expect(credit4.virtualGiftCredit).toBe(3000);
    expect(credit4.virtualSubscribeCredit).toBe(1000);
    expect(credit4.credit).toBe(0); // 永久积分
    // 使用6000积分, 因为赠送了3000积分, 所以虚拟积分应该是4000 - 6000 = -2000
    // 开始了一个新月的循环
    coinAccount = await space.billing.getCoinsAccount();
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    const credit5 = await SpaceController.getCredit(space.id);
    expect(credit5.virtualBalance).toBe(-2000);
    expect(credit5.virtualGiftCredit).toBe(0);
    expect(credit5.virtualSubscribeCredit).toBe(-2000);
    expect(credit5.credit).toBe(0); // 永久积分
    vi.useRealTimers();
  });

  test('Test space daily not used -- with 30 days one month--not across month', async () => {
    const now = dayjs('2025-06-20 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    const coinAccount = await space.billing.getCoinsAccount();

    // 消耗6000积分, 今天的积分应该是-2000
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    // 将系统时间向后调整一天, 又赠送了3000, 因此虚拟积分应该是-2000 + 3000 = 1000
    vi.useFakeTimers().setSystemTime(now.add(1, 'day').toDate());
    const credit1 = await SpaceController.getCredit(space.id);
    expect(credit1.virtualBalance).toBe(1000);
    expect(credit1.virtualGiftCredit).toBe(3000);
    expect(credit1.virtualSubscribeCredit).toBe(-2000);
    expect(credit1.credit).toBe(0); // 永久积分
    // 不消耗任何积分，只是登陆了空间站主页
    await space.billing.getCoinsAccount();
    // 将系统时间向后调整2天, 又赠送了3000, 因此虚拟积分应该是-2000 + 3000 = 1000
    vi.useFakeTimers().setSystemTime(now.add(2, 'day').toDate());
    const coinAccount2 = await space.billing.getCoinsAccount();
    const monthlyStrategy = coinAccount2.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy).toBeDefined();
    expect(monthlyStrategy?.startDate).toBe(now.toISOString());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(1000);
    expect(credit2.virtualGiftCredit).toBe(3000);
    expect(credit2.virtualSubscribeCredit).toBe(-2000);
    expect(credit2.credit).toBe(0); // 永久积分

    vi.useRealTimers();
  });

  test('Test space daily not used -- with 30 days one month--across month with 1 day', async () => {
    const now = dayjs('2025-06-29 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    // 月度结算日是5.29
    const coinAccount = await space.billing.getCoinsAccount();

    // 消耗6000积分, 今天的积分应该是-2000
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    // 将系统时间向后调整2天, 又赠送了3000, 因此虚拟积分应该是-2000 + 3000 = 1000
    // 虽然垮了月份，月的结算日应该还是06-29
    vi.useFakeTimers().setSystemTime(now.add(2, 'day').toDate());
    const coinAccount2 = await space.billing.getCoinsAccount();
    const monthlyStrategy = coinAccount2.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy).toBeDefined();
    expect(monthlyStrategy?.startDate).toBe(now.toISOString());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(1000);
    expect(credit2.virtualGiftCredit).toBe(3000);
    expect(credit2.virtualSubscribeCredit).toBe(-2000);
    expect(credit2.credit).toBe(0); // 永久积分

    vi.useRealTimers();
  });
  test('Test space daily not used -- with 30 days one month--across month with 31 days', async () => {
    const now = dayjs('2025-06-29 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    // 月度结算日是5.29
    const coinAccount = await space.billing.getCoinsAccount();

    // 消耗6000积分, 今天的积分应该是-2000
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    // 将系统时间向后调整2天, 又赠送了3000, 因此虚拟积分应该是-2000 + 3000 = 1000
    // 但是因为跨越了月份,并且时间为7.30，所以月的结算日是7.29
    vi.useFakeTimers().setSystemTime(now.add(31, 'day').toDate());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(4000);
    expect(credit2.virtualGiftCredit).toBe(3000);
    expect(credit2.virtualSubscribeCredit).toBe(1000);
    expect(credit2.credit).toBe(0); // 永久积分

    vi.useRealTimers();
  });

  // 闰年的测试
  test('Test space daily not used -- in leap year -- 29 days', async () => {
    const now = dayjs('2024-02-29 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    const coinAccount = await space.billing.getCoinsAccount();
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    const credit1 = await SpaceController.getCredit(space.id);
    expect(credit1.virtualBalance).toBe(-2000);
    // 先跨度到下一个月, 03.01
    vi.useFakeTimers().setSystemTime(now.add(1, 'day').toDate());
    const coinAccount2 = await space.billing.getCoinsAccount();
    const monthlyStrategy = coinAccount2.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy?.startDate).toBe(now.toISOString());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(1000);
    // 再跨度到下一年
    // 因为结算日是2.29，所以跨越了年份，月的结算日还是2.29, 但是，下一年不是闰年了，所以月的结算日是2.28
    vi.useFakeTimers().setSystemTime(now.add(1, 'year').toDate());
    const coinAccount3 = await space.billing.getCoinsAccount();
    const monthlyStrategy2 = coinAccount3.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy2?.startDate).toBe(dayjs('2025-02-28 12:10:00').toISOString());
    const credit3 = await SpaceController.getCredit(space.id);
    expect(credit3.virtualBalance).toBe(4000);
    expect(credit3.virtualGiftCredit).toBe(3000);
    expect(credit3.virtualSubscribeCredit).toBe(1000);
    expect(credit3.credit).toBe(0); // 永久积分
  });

  // 下一年是闰年的测试
  test('Test space daily not used -- in next leap year -- 29 days', async () => {
    const now = dayjs('2023-02-28 12:10:00');
    const {
      space: { id: spaceId },
    } = await MockContext.initUserContext();
    await db.prisma.space.update({
      where: { id: spaceId },
      data: {
        createdAt: now.toDate(),
      },
    });
    const space = await SpaceSO.init(spaceId);
    vi.useFakeTimers().setSystemTime(now.toDate());
    const coinAccount = await space.billing.getCoinsAccount();
    await coinAccount.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    const credit1 = await SpaceController.getCredit(space.id);
    expect(credit1.virtualBalance).toBe(-2000);
    vi.useFakeTimers().setSystemTime(now.add(1, 'day').toDate());
    const coinAccount2 = await space.billing.getCoinsAccount();
    const monthlyStrategy = coinAccount2.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy?.startDate).toBe(now.toISOString());
    const credit2 = await SpaceController.getCredit(space.id);
    expect(credit2.virtualBalance).toBe(1000);
    vi.useFakeTimers().setSystemTime(now.add(1, 'year').toDate());
    const coinAccount3 = await space.billing.getCoinsAccount();
    const monthlyStrategy2 = coinAccount3.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy2?.startDate).toBe(dayjs('2024-02-28 12:10:00').toISOString());
    const credit3 = await SpaceController.getCredit(space.id);
    expect(credit3.virtualBalance).toBe(4000);
    expect(credit3.virtualGiftCredit).toBe(3000);
    expect(credit3.virtualSubscribeCredit).toBe(1000);
    expect(credit3.credit).toBe(0); // 永久积分
    await coinAccount3.redeem(6000, {
      reason: 'cost-ai-completion-credit',
      completionId: 'RESOURCE_DESCRIPTION',
    });
    // 加一天，因为闰年有2.29，所以需要加一天,测试一下，跨年后的积分是否正确
    vi.useFakeTimers().setSystemTime(now.add(1, 'year').add(1, 'day').toDate());
    const coinAccount4 = await space.billing.getCoinsAccount();
    const monthlyStrategy3 = coinAccount4.strategies.find((strategy) => strategy.type === 'monthly-credit');
    expect(monthlyStrategy3?.startDate).toBe(dayjs('2024-02-28 12:10:00').toISOString());
    const credit4 = await SpaceController.getCredit(space.id);
    expect(credit4.virtualBalance).toBe(1000);
    vi.useRealTimers();
  });

  test('Space free credit', async () => {
    const { space } = await MockContext.initUserContext();
    const credit = await SpaceController.getCredit(space.id);
    expect(credit.virtualBalance).toBe(4000);
    expect(credit.virtualGiftCredit).toBe(3000);
    expect(credit.virtualSubscribeCredit).toBe(1000);

    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    // Member user to join the space
    await space.joinUser(user2.id, rootTeam.id);
    const newCredit = await SpaceController.getCredit(space.id);
    expect(newCredit.virtualBalance).toBe(5000);
    expect(newCredit.virtualGiftCredit).toBe(3000);
    expect(newCredit.virtualSubscribeCredit).toBe(2000);
  });

  test('old year free space', async () => {
    const { space } = await MockContext.initUserContext();
    // 将系统时间往后调整1.5年
    vi.useFakeTimers().setSystemTime(dayjs().add(1.5, 'year').toDate());
    const credit = await SpaceController.getCredit(space.id);
    expect(credit.virtualBalance).toBe(4000);
    expect(credit.virtualGiftCredit).toBe(3000);
    expect(credit.virtualSubscribeCredit).toBe(1000);
    // Member user to join the space
    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    await space.joinUser(user2.id, rootTeam.id);
    const newCredit = await SpaceController.getCredit(space.id);
    expect(newCredit.virtualBalance).toBe(5000);
    expect(newCredit.virtualGiftCredit).toBe(3000);
    expect(newCredit.virtualSubscribeCredit).toBe(2000);
    vi.useRealTimers();
  });
});

describe('space coin transaction test', () => {
  test('Test delete member coin transaction vo', async () => {
    const { space } = await MockContext.initUserContext();
    const { user: user2 } = await MockContext.initUserContext();
    const rootTeam = await space.getRootTeam();
    await space.joinUser(user2.id, rootTeam.id);
    const member2 = await space.getMemberByUserId(user2.id);
    const coinAccount = await space.billing.getCoinsAccount();
    await coinAccount.earn(
      1000,
      'CREDIT',
      {
        reason: 'gm',
        description: 'test',
      },
      user2.id,
    );

    await coinAccount.redeem(
      1000,
      {
        reason: 'gm',
        description: 'test',
      },
      'VIRTUAL',
      undefined,
      user2.id,
    );
    // 将user2踢出空间
    await member2?.delete();

    const transaction = await coinAccount.getTransactionsWithPagination();
    expect(transaction.pagination.total).toBe(3);
    expect(transaction.data.length).toBe(3);
    expect(transaction.data[1].member).toStrictEqual({
      avatar: user2.avatar,
      id: null,
      name: user2.name,
      createdAt: undefined,
      email: user2.email,
      deleted: true,
      isSpaceOwner: undefined,
      isGuest: undefined,
      teams: undefined,
      roles: undefined,
      type: 'Member',
      userId: user2.id,
      relationType: 'USER',
    });
  });
});
