/* eslint-disable no-undef */
import type { DataStreamWriter } from 'ai';
import _ from 'lodash';
import { getAIIntentTypesConfig } from '@bika/contents/config/client/ai/wizard';
import { AIModelServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { getAINodeSystemPrompt } from '@bika/contents/config/server/ai-prompts/ai-node.prompt';
import { getServerLocaleContext } from '@bika/contents/i18n/server';
import { AISO } from '@bika/domains/ai/server/ai-so';
import { ResolutionResultInfo, IStreamChatPrompt, AIModelOptions } from '@bika/domains/ai/server/types';
import { AINodeIntentParams, AIIntentParams, AIMessageBO, AiNodeBO, AIModelDef } from '@bika/types/ai/bo';
import { AIIntentUIVO, AIResolveVO } from '@bika/types/ai/vo';
import type { AIChatOption } from '@bika/types/ai/vo';
import { AIIntentResolver } from './resolvers';
import { DeepSeekHandler } from '../integration/server/handlers/deepseek-handler';
import { determineIntegrationHandler } from '../integration/server/handlers/handler-factory';
import { OpenAIHandler } from '../integration/server/handlers/openai-handler';
import { IntegrationSO } from '../integration/server/integration-so';
import { IntegrationModel } from '../integration/server/types';
import { AINodeSO } from '../node-resources/ai-agent/ai-node-so';
import { SpaceSO } from '../space/server/space-so';
import { UserSO } from '../user/server/user-so';

export class AINodeIntentResolver extends AIIntentResolver<AINodeIntentParams> {
  async onInit(): Promise<boolean> {
    return true;
  }

  private getDefaultPromptsIntentUI(): AIIntentUIVO {
    const prompts = getAIIntentTypesConfig(getServerLocaleContext('en')).COPILOT.prompts;
    const defaultPromtsIntentUI: AIIntentUIVO = {
      type: 'PROMPT',
      title: 'You can say something like',
      prompts: _.sampleSize(prompts, 5),
      hiddenInput: false,
    };
    return defaultPromtsIntentUI;
  }

  async getPrologue() {
    return undefined;
  }

  async getOptions(): Promise<AIChatOption[]> {
    return [
      {
        label: 'Ask',
        value: 'ask',
        disabled: true,
      },
      {
        label: 'Edit',
        value: 'edit',
        disabled: true,
      },
    ];
  }

  async doResolve(
    _resolver: AIResolveVO,
    user: UserSO,
    chatHistories: AIMessageBO[],
    dataStreamWriter?: DataStreamWriter,
  ): Promise<ResolutionResultInfo> {
    const prompt = await this.parsePrompt(user, chatHistories);
    const model = await this.getModel();
    // todo handle error
    const { message, usage } = await AISO.streamChat(
      // chatHistories,
      prompt,
      {
        ...model,
        onFinish: (_data) => {},
        onChunk: (_chunk) => {},
        dataStreamWriter,
      },
      {
        returnPrompts: this.getPrompts(user.locale),
      },
    );

    return {
      intentParam: this.intentParams,
      status: 'DIALOG',
      message,
      usage,
    };
  }

  async doComplete(): Promise<{ nextIntent: AIIntentParams | null }> {
    return {
      nextIntent: null,
    };
  }

  async parsePrompt(user: UserSO, chatHistories: AIMessageBO[]): Promise<IStreamChatPrompt> {
    const nodeId = this.intentParams.nodeId!;
    const aiNodeSO = await AINodeSO.init(nodeId);
    const bo: AiNodeBO = await aiNodeSO.toBO();
    const nodeIds = await aiNodeSO.getResourceIds();
    const automationIds = Array.from(
      new Set(bo.tools?.filter((i) => i.type === 'AUTOMATION')?.map((o) => o.automationId) || []),
    );

    const messages = chatHistories.slice(-20);
    const node = aiNodeSO.toNodeSO();
    const systemPrompt = getAINodeSystemPrompt({
      spaceId: node.spaceId,
      parentId: node.parentId || '',
      nodeIds,
      automationIds,
      userId: user.id,
      task: bo.prompt,
    });
    return {
      system: systemPrompt,
      messages,
      skillsets: [
        ...(bo.skillsets || []),
        { kind: 'preset', key: 'default' },
        { kind: 'preset', key: 'bika-space' },
        // { kind: 'preset', key: 'unit' },
        { kind: 'preset', key: 'bika-image', includes: ['generate_image'] },
      ],
      maxSteps: 10,
      user, // for tool
      space: await SpaceSO.init(aiNodeSO.spaceId),
    };
  }

  async getModel(): Promise<Pick<AIModelOptions, 'model' | 'apiKey' | 'baseUrl' | 'organizationId'>> {
    const nodeId = this.intentParams.nodeId!;
    const aiNodeSO = await AINodeSO.init(nodeId);
    const bo: AiNodeBO = await aiNodeSO.toBO();

    // Helper function to create model config
    const createModelConfig = (handler: OpenAIHandler | DeepSeekHandler) => ({
      model: (bo.model as AIModelDef) || handler.defaultModel,
      apiKey: handler.apiKey,
      baseUrl: handler.baseUrl,
      organizationId: handler.organizationId,
    });

    // Check integration ID first
    if (bo.integrationId) {
      const integration = await IntegrationSO.initMaybeNull(bo.integrationId);
      const handler = integration?.getHandler<OpenAIHandler | DeepSeekHandler>();
      if (handler) {
        return createModelConfig(handler);
      }
    }

    // Check custom integration
    if (bo.aiModel && typeof bo.aiModel === 'object' && bo.aiModel.apiKey) {
      const handler = determineIntegrationHandler<OpenAIHandler | DeepSeekHandler>({
        bo: bo.aiModel,
        type: bo.aiModel.type,
      } as unknown as IntegrationModel);
      return createModelConfig(handler);
    }

    // Check server config model
    if (bo.model && Object.keys(AIModelServerConfig).includes(bo.model)) {
      return { model: bo.model as AIModelDef };
    }

    // Check string-based AI model
    if (bo.aiModel && typeof bo.aiModel === 'string') {
      return {
        model: bo.aiModel === 'DEEPSEEK' ? 'deepseek-r1' : 'gpt-4.1',
      };
    }

    // Default fallback
    return { model: 'gpt-4.1' };
  }
}
