'use client';

import { useTRPC } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import { updateNodeChildrenItem } from '@bika/domains/space/client/sidebar/utils/match-template-util';
import { FolderSchema } from '@bika/types/node/bo';
import type { NodeTreeVO } from '@bika/types/node/vo';
import { useSpaceId } from '@bika/types/space/context';
import { bo2CreateDTO } from '@bika/types/utils/bo-vo';
import { useBuildInstallGlobalStore } from '../../space/client/intercepter/use-build-install-global-store';

export const useInstaller = () => {
  const trpc = useTRPC();
  const spaceId = useSpaceId();
  const { i } = useLocale();

  const { setData } = useBuildInstallGlobalStore();

  const installTemplates = async (templateList: string[], onStart?: () => void) => {
    const rootNode = await trpc.space.getRootNode.query({
      spaceId,
    });
    const dtoList = [];
    for (const templateId of templateList) {
      const template = await trpc.template.detail.query({
        templateId,
      });
      const resource = FolderSchema.parse({
        resourceType: 'FOLDER',
        name: template.name,
        children: template.current.data.resources,
        templateId,
        icon: template.current.data.cover,
      });

      const createDTOs = bo2CreateDTO([resource], {
        spaceId,
        parentId: rootNode.id,
      });

      dtoList.push(...createDTOs.map((item) => ({ ...item, templateName: i(template.name) })));
    }

    onStart?.();
    setData({
      toolInvocation: null,
      dto: dtoList,
      process: 0,
    });
    for (let i = 0; i < dtoList.length; i++) {
      const dto = dtoList[i];
      const newNode = await trpc.node.create.mutate(dto);

      // 更新 rootNode
      const newNodeData = updateNodeChildrenItem(rootNode.children || [], dto.parentId, {
        ...newNode,
        parentId: dto.parentId,
      } as NodeTreeVO);

      setData({
        toolInvocation: null,
        dto: dtoList,
        index: i,
        process: (i + 1) / dtoList.length,
      });

      await new Promise((resolve) => {
        setTimeout(resolve, 1000);
      });
    }
  };

  return installTemplates;
};
