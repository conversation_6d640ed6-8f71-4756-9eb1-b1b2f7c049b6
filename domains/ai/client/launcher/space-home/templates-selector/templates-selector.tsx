import React from 'react';
import { useLocale, type LocaleContextProps } from '@bika/contents/i18n';
import { TalkDetailVO } from '@bika/types/space/vo';
import { TemplateCardVO } from '@bika/types/template/vo';
import { Stack } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';
import { useTemplateList } from './use-template-list';
import { TalksSelectorRenderer } from '../talks-selector/talks-selector-renderer';

interface TemplateSelectorProps {
  templateIds: string[];
}

// Helper function to map template data to ISelectorItem
const mapTemplateToISelectorItem = (template: TemplateCardVO, i: LocaleContextProps['i']): TalkDetailVO => ({
  type: 'template',
  id: template.templateId,
  templateId: template.templateId,
  template,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  skillsets: [],
  // name: i(template.name),
  // description: i(template.description),
  // icon,
});
export const TemplateSelector = ({ templateIds }: TemplateSelectorProps) => {
  const { data, loading } = useTemplateList({ templateIds });
  const { i } = useLocale();

  const mappedAgents: TalkDetailVO[] = React.useMemo(() => {
    if (!data || !Array.isArray(data)) {
      return [];
    }

    return data.map((template) => mapTemplateToISelectorItem(template, i));
  }, [data, i]);

  if (loading) {
    return (
      <Stack justifyContent="center" direction="row">
        <Skeleton pos="SELECTOR" />
      </Stack>
    );
  }

  if (mappedAgents.length === 0) {
    return null;
  }

  return <TalksSelectorRenderer value={mappedAgents} selected={undefined} onSelectedChange={() => {}} />;
};
