import React, { useRef, useState, useEffect } from 'react';
import { TalkDetailVO } from '@bika/types/space/vo';
import { IconButton } from '@bika/ui/button';
import ChevronLeftOutlined from '@bika/ui/icons/components/chevron_left_outlined';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import { Box } from '@bika/ui/layouts';
import { SelectorTalkCardRenderer } from './selector-talk-card-renderer';
// import type { ISelectorItem } from './type';
import type { AgentListProps } from '../agents-selector/agents-selector';

const AGENT_LIST_WIDTH = 720;

const ITEM_WIDTH = (AGENT_LIST_WIDTH - 16 * 3) / 4;

const CONST_GAP_WIDTH = 16;

type Props = AgentListProps & {
  value: TalkDetailVO[];
};
export function TalksSelectorRenderer(props: Props) {
  const agents = props.value;

  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const checkScrollButtons = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
  }, [agents]);

  const scroll = (direction: 'left' | 'right') => {
    const xWidth = ITEM_WIDTH + 16;
    if (scrollRef.current) {
      scrollRef.current.scrollBy({
        left: direction === 'left' ? -1 * xWidth : xWidth,
        behavior: 'smooth',
      });
    }
  };

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <IconButton
        variant="soft"
        onClick={() => scroll('left')}
        sx={{
          visibility: canScrollLeft ? 'visible' : 'hidden',
          backgroundColor: 'var(--bg-blur)',
          border: '1px solid var(--border-default)',
          backdropFilter: 'blur(8px)',
          borderRadius: '6px',
          boxShadow: 'var(--shadow-default)',
          '&:hover': {
            backgroundColor: 'color-mix(in srgb, var(--bg-blur) 100%, var(--hover) 100%)',
          },
        }}
      >
        <ChevronLeftOutlined />
      </IconButton>

      <Box
        ref={scrollRef}
        onScroll={checkScrollButtons}
        sx={{
          display: 'flex',
          overflowX: 'auto',
          scrollBehavior: 'smooth',
          width: AGENT_LIST_WIDTH,
          justifyContent: agents.length < 4 ? 'center' : 'unset',
          gap: `${CONST_GAP_WIDTH}px`,
          '&::-webkit-scrollbar': { display: 'none' },
        }}
      >
        {agents.map((item, index) => {
          const isSelected = props.selected?.id === item?.id;
          return (
            <SelectorTalkCardRenderer
              selected={props.selected}
              width={ITEM_WIDTH}
              onSubmit={props.onSubmit}
              onSelectedChange={props.onSelectedChange}
              isSelected={isSelected}
              value={item}
              key={index}
            />
          );
        })}
      </Box>

      <IconButton
        variant="soft"
        onClick={() => scroll('right')}
        sx={{
          visibility: canScrollRight ? 'visible' : 'hidden',
          backgroundColor: 'var(--bg-blur)',
          border: '1px solid var(--border-default)',
          backdropFilter: 'blur(8px)',
          borderRadius: '6px',
          boxShadow: 'var(--shadow-default)',
          '&:hover': {
            backgroundColor: 'color-mix(in srgb, var(--bg-blur) 100%, var(--hover) 100%)',
          },
        }}
      >
        <ChevronRightOutlined />
      </IconButton>
    </Box>
  );
}
