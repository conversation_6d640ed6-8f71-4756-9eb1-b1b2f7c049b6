/* eslint-disable no-param-reassign */
import https from 'https';
import axios, { AxiosRequestConfig } from 'axios';
import { isBlank, isNotBlank } from 'basenext/utils/string';
import _ from 'lodash';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { NodeResourceSO } from '@bika/domains/node/server/types';
import {
  getUnknownErrorMessage,
  pathHelper,
  render,
  renderObjectWithLodash,
  replaceVariables,
} from '@bika/domains/shared/server';
import {
  BaseResourceNode,
  DatabaseTypeInput,
  FormInput,
  IWebhookInputBody,
  LoopActionInput,
  PrevActionInput,
  WebhookActionOutput,
} from '@bika/types/automation/bo';
import { FilterCondition, RecordData } from '@bika/types/database/bo';
import { LocaleType } from '@bika/types/system';
import { MemberVO } from '@bika/types/unit/vo';
import { FilterSO } from '../../database/server';
import { FilterConditionMigrateAdapter } from '../../database/shared/filter-condition-migrate-adapter';

export function convertPrevActionInput(
  input: PrevActionInput | LoopActionInput,
  converter: BObuildIdConverter,
  automationTemplateId?: string,
) {
  const { actionId, actionTemplateId } = input;
  const { instanceId, templateId } = converter.convert({
    instanceId: actionId,
    templateId: actionTemplateId,
    prevKey: automationTemplateId,
    notFoundErrMsg: `PreActionId [${actionId}] not found in automation`,
  });
  return { ...input, actionId: instanceId, actionTemplateId: templateId };
}

export function convertDatabaseTypeInput<T extends DatabaseTypeInput>(input: T, converter: BObuildIdConverter) {
  const databaseTypeInput = _.cloneDeep(input);
  const { type, databaseId, databaseTemplateId } = databaseTypeInput;

  const { instanceId, templateId } = converter.convert({
    instanceId: databaseId,
    templateId: databaseTemplateId,
    notFoundErrMsg: `Automation linked database [${databaseId}] not in current folder`,
  });
  databaseTypeInput.databaseId = instanceId;
  databaseTypeInput.databaseTemplateId = templateId;

  switch (type) {
    case 'DATABASE':
      break;
    case 'DATABASE_VIEW': {
      const { viewId, viewTemplateId } = databaseTypeInput;
      const view = converter.convert({
        instanceId: viewId,
        templateId: viewTemplateId,
        prevKey: databaseTemplateId,
        notFoundErrMsg: `Automation linked view [${viewId}] not in database [${databaseId}]`,
      });
      databaseTypeInput.viewId = view.instanceId;
      databaseTypeInput.viewTemplateId = view.templateId;
      break;
    }
    case 'DATABASE_WITH_FILTER': {
      const { filters } = databaseTypeInput;
      if (!filters || (!filters.conditions.length && !filters.conds?.length)) {
        break;
      }
      // 这里没有DatabaseSO, 不能使用filterSO, 直接兼容迁移
      // 可能是已经落库的filters拿出来转换, 这里涉及改变fieldId和fieldTemplateId, 原来的filter兼容后返回
      const filterConditions: FilterCondition[] =
        filters.conds || FilterConditionMigrateAdapter.migrate(filters.conditions ?? []);
      // 添加到 filters.conditions
      filters.conds = filterConditions;
      const relationFunc = converter.getRelationFunc(databaseTemplateId);
      for (const fieldFilterCondition of filters.conds ?? []) {
        const { fieldId, fieldTemplateId, clause } = fieldFilterCondition;
        const field = converter.convert({
          instanceId: fieldId,
          templateId: fieldTemplateId,
          prevKey: databaseTemplateId,
          notFoundErrMsg: `Automation linked field [${fieldId}] not in database [${databaseId}]`,
        });
        fieldFilterCondition.fieldId = field.instanceId;
        fieldFilterCondition.fieldTemplateId = field.templateId;
        clause.value = replaceVariables(clause.value, relationFunc);
      }
      // console.debug('改完前端的filters', filters);
      break;
    }
    case 'DATABASE_FIELD':
    case 'DATETIME_FIELD_REACHED': {
      const { fieldId, fieldTemplateId } = databaseTypeInput;
      const field = converter.convert({
        instanceId: fieldId,
        templateId: fieldTemplateId,
        prevKey: databaseTemplateId,
        notFoundErrMsg: `Automation linked field [${fieldId}] not in database [${databaseId}]`,
      });
      databaseTypeInput.fieldId = field.instanceId;
      databaseTypeInput.fieldTemplateId = field.templateId;
      break;
    }
    case 'RECORD_BODY':
    case 'SPECIFY_RECORD_BODY': {
      const relationFunc = converter.getRelationFunc(databaseTemplateId);
      // 替换 recordId
      if (type === 'SPECIFY_RECORD_BODY') {
        const convertRecord = () => {
          const recordId = databaseTypeInput.recordId;
          if (typeof recordId === 'string') {
            return replaceVariables(recordId, relationFunc);
          }
          return recordId.map((id) => replaceVariables(id, relationFunc));
        };
        databaseTypeInput.recordId = convertRecord();
      }
      // 替换 data
      const { data } = databaseTypeInput;
      if (Object.keys(data).length === 0) {
        break;
      }

      const newData: RecordData = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          // 替换字段 key
          const fieldKey = relationFunc(key);
          if (!fieldKey) {
            throw new Error(`Automation linked field [${key}] not in database [${databaseId}]`);
          }
          // 替换 value 中的变量
          newData[fieldKey] = replaceVariables(data[key], relationFunc);
        }
      }
      databaseTypeInput.data = newData;
      break;
    }
    default:
      throw new Error(`Unsupported database type input type: ${type}`);
  }
  return databaseTypeInput;
}

export function validateDatabaseTypeInput<T extends DatabaseTypeInput>(input: T): boolean {
  const { databaseId, databaseTemplateId } = input;
  // 表ID或模板ID必须有一个，且不能为空
  if (isBlank(databaseId) && isBlank(databaseTemplateId)) {
    return false;
  }
  switch (input.type) {
    case 'DATABASE_VIEW': {
      const { viewId, viewTemplateId } = input;
      return isNotBlank(viewId) || isNotBlank(viewTemplateId);
    }
    case 'DATABASE_FIELD':
    case 'DATETIME_FIELD_REACHED': {
      const { fieldId, fieldTemplateId } = input;
      return isNotBlank(fieldId) || isNotBlank(fieldTemplateId);
    }
    case 'SPECIFY_RECORD_BODY': {
      const { recordId } = input;
      if (typeof recordId === 'string') {
        return isNotBlank(recordId);
      }
      return recordId.length > 0;
    }
    default:
      break;
  }
  return true;
}

export async function fillInDatabaseTypeInput<T extends DatabaseTypeInput>(
  input: T,
  templateNodeId?: string,
): Promise<T> {
  if (!templateNodeId) {
    return input;
  }
  let databaseTypeInput = _.cloneDeep(input);
  // 不直接查库获取Database，可能用不上，减少不必要的查询
  let database: DatabaseSO | undefined;
  async function getDatabase(): Promise<DatabaseSO> {
    if (database) {
      return database;
    }
    const { databaseId, databaseTemplateId } = databaseTypeInput;
    database = await DatabaseSO.findDatabase({ templateNodeId, databaseId, databaseTemplateId });
    return database;
  }
  // 填充各种实例ID
  try {
    const { databaseId } = input;
    if (!databaseId) {
      database = await getDatabase();
      databaseTypeInput.databaseId = database.id;
    }
    switch (input.type) {
      case 'DATABASE_VIEW': {
        const { viewId, viewTemplateId } = input;
        if (!viewId) {
          database = await getDatabase();
          const view = await database.getViewByViewKey({ viewTemplateId });
          databaseTypeInput = { ...databaseTypeInput, viewId: view.id };
        }
        break;
      }
      case 'DATABASE_WITH_FILTER': {
        const { filters } = input;
        if (!filters || (!filters.conditions.length && !filters.conds?.length)) {
          break;
        }
        const theDatabase = await getDatabase();
        const newFilters = _.cloneDeep(filters);
        // 补充 fieldId
        for (const condition of newFilters.conditions) {
          const { fieldId, fieldTemplateId } = condition;
          if (!fieldId && fieldTemplateId) {
            const field = theDatabase.getFields().find((f) => f.templateId === fieldTemplateId);
            condition.fieldId = field?.id;
          }
        }
        for (const condition of newFilters.conds || []) {
          const { fieldId, fieldTemplateId } = condition;
          if (!fieldId && fieldTemplateId) {
            const field = theDatabase.getFields().find((f) => f.templateId === fieldTemplateId);
            condition.fieldId = field?.id;
          }
        }
        const filterSO = new FilterSO(theDatabase, newFilters);
        databaseTypeInput = { ...databaseTypeInput, filters: filterSO.toVO() };
        break;
      }
      case 'DATABASE_FIELD':
      case 'DATETIME_FIELD_REACHED': {
        const { fieldId, fieldTemplateId } = input;
        if (!fieldId && fieldTemplateId) {
          const field = (await getDatabase()).getFields().find((f) => f.templateId === fieldTemplateId);
          databaseTypeInput = { ...databaseTypeInput, fieldId: field?.id };
        }
        break;
      }
      case 'RECORD_BODY':
      case 'SPECIFY_RECORD_BODY': {
        const { data } = input;
        if (Object.keys(data).length === 0) {
          break;
        }
        const newData: Record<string, unknown> = {};
        const databaseSO = await getDatabase();
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            const fieldId = databaseSO.findFieldByFieldKey(key)?.id;
            newData[fieldId || key] = data[key];
          }
        }
        databaseTypeInput = { ...databaseTypeInput, fieldKeyType: 'ID', data: newData };
        break;
      }
      default:
        break;
    }
  } catch (_e) {
    // do nothing
  }
  return databaseTypeInput;
}

export function convertFormInputTargetId(input: FormInput, converter: BObuildIdConverter) {
  const formInput = _.cloneDeep(input);
  const { formId, formTemplateId } = formInput;
  const { instanceId, templateId } = converter.convert({
    instanceId: formId,
    templateId: formTemplateId,
    notFoundErrMsg: `Automation linked form [${formId}] not in current folder`,
  });
  formInput.formId = instanceId;
  formInput.formTemplateId = templateId;
  return formInput;
}

export async function fillInFormInput(input: FormInput, templateNodeId?: string): Promise<FormInput> {
  const { formId, formTemplateId } = input;
  // 已存在表单ID、模板ID为空或不存在模板文件夹ID，直接返回
  if (formId || isBlank(formTemplateId) || !templateNodeId) {
    return input;
  }
  try {
    const formSO = await FormSO.findForm({ templateNodeId, formTemplateId });
    return { ...input, formId: formSO.id };
  } catch (_e) {
    // do nothing
  }
  return input;
}

export function buildBaseResourceNode<T extends NodeResourceSO>(resource: T, locale?: LocaleType): BaseResourceNode {
  const node = resource.toNodeSO();
  const { id, spaceId } = node;
  return {
    id,
    name: node.getName(locale),
    url: pathHelper.getNodeUrl(spaceId, id),
  };
}

export function getFakeMemberVO(): MemberVO {
  return {
    id: 'member_id',
    name: 'Member Name',
    type: 'Member',
    email: '',
    userId: 'user_id',
    isSpaceOwner: false,
    isGuest: false,
    relationType: 'USER',
  };
}

export function getFakeMissionOutputVO(withDueAt: boolean = true) {
  return {
    id: 'mission_id',
    name: 'Mission Name',
    url: pathHelper.getSpaceMissionUrl('space_id', 'mission_id'),
    dueAt: withDueAt ? new Date().toISOString() : undefined,
  };
}

function parserHeader(headers: { key: string; value: string }[]) {
  if (!headers) return {};
  return headers.reduce(
    (pre, next) => {
      const { key, value } = next;
      if (key.toLowerCase() === 'content-type') {
        pre['content-type'] = value;
      } else {
        pre[key] = value;
      }
      return pre;
    },
    {} as { [key: string]: string },
  );
}

export async function sendRequest(
  method: string,
  headers: { key: string; value: string }[],
  url: string,
  body?: IWebhookInputBody,
  props?: { [key: string]: unknown },
  timeout?: number,
): Promise<WebhookActionOutput> {
  let contentType;
  let bodyData;
  const formData = new FormData();
  if (body) {
    switch (body.type) {
      case 'form-data':
        contentType = 'application/x-www-form-urlencoded';
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        body.formData.forEach((item: { key: string; value?: any }) => {
          if (item.value) {
            formData.append(item.key, item.value);
          }
        });
        bodyData = formData;
        break;
      case 'raw':
        contentType = 'text/plain';
        bodyData = body.data;

        if (body.format === 'json') {
          contentType = 'application/json';

          if (typeof body.data === 'object' && body.data !== null) {
            renderObjectWithLodash(body.data, props);
          }

          bodyData = JSON.stringify(body.data);
        } else {
          bodyData = render(bodyData as string, props);
        }
        break;
      default:
        contentType = 'application/json';
    }
  }

  const fetchTimeout = () => {
    if (!timeout || timeout <= 0) {
      return 60000;
    }
    const maxTimeout = process.env.REQUEST_MAX_TIMEOUT ? parseInt(process.env.REQUEST_MAX_TIMEOUT, 10) : 5 * 60000;
    if (timeout > maxTimeout) {
      return maxTimeout;
    }
    return timeout;
  };
  const withBody = ['post', 'patch', 'put', 'delete'].includes(method.toLowerCase());
  try {
    const axiosConfig: AxiosRequestConfig = {
      method,
      url,
      headers: contentType ? { 'content-type': contentType, ...parserHeader(headers) } : parserHeader(headers),
      data: withBody ? bodyData : undefined,
      timeout: fetchTimeout(),
      httpsAgent: new https.Agent({ rejectUnauthorized: false }),
    };
    const res = await axios(axiosConfig);
    if (res.status >= 200 && res.status < 300) {
      return { status: res.status, body: res.data };
    }
    throw new Error(`${res.status} ${res.statusText}`);
  } catch (error: unknown) {
    throw new Error(getUnknownErrorMessage(error));
  }
}
