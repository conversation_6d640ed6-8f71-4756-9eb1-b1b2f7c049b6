import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { UserSettingTabType } from '@bika/types/space/bo';
import { useGlobalContext } from '@bika/types/website/context';
import ApiOutlined from '@bika/ui/icons/components/api_outlined';
import LockOutlined from '@bika/ui/icons/components/lock_outlined';
import NotificationOutlined from '@bika/ui/icons/components/notification_outlined';
import Star2Outlined from '@bika/ui/icons/components/star_2_outlined';
import UserOutlined from '@bika/ui/icons/components/user_outlined';
import TimeOutlined from '@bika/ui/icons/doc_hide_components/time_outlined';
import { Box } from '@bika/ui/layouts';
import { Tab } from '@bika/ui/tabs';
import { Typography } from '@bika/ui/texts';
import { MemberItem } from '../../unit/client/member-item';

export const UserSettingsTabButtons = () => {
  const { authContext: auth, appEnv } = useGlobalContext();
  const { t } = useLocale();

  const user = auth.me?.user;

  return (
    <>
      <Typography textColor="var(--text-secondary)" level="title-lg" sx={{ py: 2 }}>
        {t.user.account}
      </Typography>
      {user && (
        <Box sx={{ mb: 1 }}>
          <MemberItem
            disabled
            id={user.id}
            name={user.name}
            userId={user.id}
            email={user.email}
            avatar={user.avatar}
            type="Member"
            isGuest={false}
            isSpaceOwner={false}
            relationType="USER"
          />
        </Box>
      )}
      <Tab value={'USER_PROFILE' as UserSettingTabType}>
        <UserOutlined color="var(--text-secondary)" />
        {t.user.profile}
      </Tab>
      <Tab value={'USER_SECURITY' as UserSettingTabType}>
        <LockOutlined color="var(--text-secondary)" />
        {t.settings.account.connected_account}
      </Tab>
      <Tab value={'USER_INTEGRATION' as UserSettingTabType}>
        <NotificationOutlined color="var(--text-secondary)" />
        {t.settings.account.notification}
      </Tab>
      <Tab value={'USER_SESSIONS' as UserSettingTabType}>
        <TimeOutlined color="var(--text-secondary)" />
        {t.settings.account.session_logs}
      </Tab>
      <Tab value={'USER_DEVELOPER' as UserSettingTabType}>
        <ApiOutlined color="var(--text-secondary)" />
        {t.settings.account.api}
      </Tab>
      {appEnv !== 'SELF-HOSTED' && (
        <Tab value={'USER_REFERRAL' as UserSettingTabType}>
          <Star2Outlined color="var(--text-secondary)" />
          {t.settings.account.referral}
        </Tab>
      )}
    </>
  );
};
