import type { <PERSON><PERSON> } from './vo-member';
import { TeamVO } from './vo-team';

export function mockRootTeam(): TeamVO {
  return {
    id: 'story',
    name: 'Root Team',
    type: 'Team',
    isGuest: false,
  };
}

export function mockGuestRootTeam(): TeamVO {
  return {
    id: 'story',
    name: 'Guest Root Team',
    type: 'Team',
    isGuest: true,
  };
}

export function mockMemberVO(): MemberVO {
  return {
    id: 'member',
    name: 'Member',
    type: 'Member',
    userId: 'user123',
    email: '<EMAIL>',
    isGuest: false,
    isSpaceOwner: false,
    relationType: 'USER',
  };
}

export * from './default-anonymous';
