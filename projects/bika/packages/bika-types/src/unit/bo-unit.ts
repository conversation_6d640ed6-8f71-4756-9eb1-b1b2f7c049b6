import { z } from 'zod';
import { iStringSchema } from '../i18n/bo';

const UnitTypes = ['ROLE', 'MEMBER', 'TEAM'] as const;

const UnitTypeSchema = z.enum(UnitTypes);

const BaseUnitSchema = z.object({
  type: UnitTypeSchema,
  name: iStringSchema.optional(),
  id: z.string().optional(),
  // Will trace back to the template ID, not easily deleted
  templateId: z.string().optional(),
});

// Preset role, can set permissions
export const UnitRoleSchema = BaseUnitSchema.extend({
  type: z.literal(UnitTypeSchema.enum.ROLE),

  // This role will automatically be assigned to new members, can be configured
  // autoJoinNewMember: z.boolean().optional(),
});

export type UnitRole = z.infer<typeof UnitRoleSchema>;

// Member must have an ID, cannot use template id
export const UnitMemberSchema = BaseUnitSchema.extend({
  type: z.literal(UnitTypeSchema.enum.MEMBER),
});

export const UnitTeamSchema = BaseUnitSchema.extend({
  type: z.literal(UnitTypeSchema.enum.TEAM),
});

export const UnitSchema = z.union([UnitRoleSchema, UnitMemberSchema, UnitTeamSchema]);
export type Unit = z.infer<typeof UnitSchema>;

export const UnitMemberRelationTypeSchema = z.enum(['USER', 'AI']);
export type UnitMemberRelationType = z.infer<typeof UnitMemberRelationTypeSchema>;
