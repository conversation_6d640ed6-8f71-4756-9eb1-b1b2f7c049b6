import { z } from 'zod';
import { BasePaginationInfoSchema } from '../shared';
import { UnitMemberRelationTypeSchema } from './bo-unit';
import { MemberUnitType, UnitBaseSchema } from './vo-base';
import { RoleVOSchema } from './vo-role';
import { TeamVOSchema } from './vo-team';
import { AvatarLogoSchema } from '../system/avatar';

export const MemberVOSchema = UnitBaseSchema.extend({
  type: MemberUnitType,
  userId: z.string(),
  email: z.string().nullable(),
  avatar: AvatarLogoSchema.optional(),
  nickName: z.string().optional(),
  isGuest: z.boolean(),
  isSpaceOwner: z.boolean().optional(),
  teams: z.array(TeamVOSchema).optional(),
  roles: z.array(RoleVOSchema).optional(),
  relationType: UnitMemberRelationTypeSchema,
});
export type MemberVO = z.infer<typeof MemberVOSchema>;

export const MemberPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(MemberVOSchema),
});

export type MemberPaginationVO = z.infer<typeof MemberPaginationVOSchema>;
