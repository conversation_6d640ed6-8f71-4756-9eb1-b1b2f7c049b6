import assert from 'assert';
import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Stack, Typography, AspectRatio, Textarea } from '@mui/joy';
import React, { useState } from 'react';
import { relativePathDisplay } from '@bika/ui/components/avatar/index';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { useLocale } from '@bika/contents/i18n/context';

// interface AIPhotoTabProps {
//   callback: (image: string) => void;
// }

// export function AIPhotoTab(props: AIPhotoTabProps) {
//   return <></>;
// }

// import { AISO } from '../services/aiso'; // Adjust import path as needed

interface IImage {
  attachmentId: string;
  relativePath: string;
}

export interface AIImageGeneratorProps {
  onAIGenerate?: (userPrompt: string) => Promise<IImage[]>;
  placeholder?: string;
  defaultPrompt?: string; // Default prompt for AI image generation
}

type Props = AIImageGeneratorProps & {
  onApply: (image: IImage, prompt: string) => void;
};
/**
//  * 这是一个用于 AI 生成图片的控件(@mui/joy)，调用 GPT Image或生图 ai 模型进行图片生成 (接口AISO.generateImage) 。
//  *
//  * 他的使用过程：在prompt中输入描述，点击生成图片按钮，等待图片生成完成后，可以预览图片，
//  * 可以点击“重新生成”重新生成图像，
//  * 或“应用 Apply”，调用 Callback
 *
 * @returns
 */
export function AIImageTab(props: Props) {
  const ctx = useUIFrameworkContext();
  const { onApply } = props;
  const [prompt, setPrompt] = useState(() => props.defaultPrompt || '');
  const [images, setImages] = useState<IImage[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { t } = useLocale();

  const generateImage = async (userPrompt: string) => {
    if (!prompt.trim()) return;

    assert(props.onAIGenerate, 'onAIGenerate callback must be provided');

    setLoading(true);
    setError(null);
    setSelectedIndex(null);

    try {
      // Generate 4 different images (using different mock URLs for demo)
      const generatedImages = await props.onAIGenerate(userPrompt);
      setImages(generatedImages);
    } catch (err) {
      setError(err instanceof Error ? err.message : '图片生成失败');
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (index: number) => {
    setSelectedIndex(index);
    // Automatically apply when image is selected
    if (images[index] && onApply) {
      onApply(images[index], prompt);
    }
  };

  return (
    <Card sx={{ padding: '16px' }}>
      <CardContent>
        <Stack>
          <Typography level="title-md">{t.ai.ai_image_generation}</Typography>

          <Textarea
            placeholder={props.placeholder || 'Describe the image you want to generate...'}
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={loading}
            minRows={2}
            sx={{
              background: 'var(--bg-controls)',
              mt: '8px',
              mb: '16px',
            }}
          />

          <Button
            onClick={async () => generateImage(prompt)}
            loading={loading}
            disabled={!prompt.trim()}
            color="primary"
          >
            {loading ? t.ai.generating : t.ai.generate_image}
          </Button>

          {error && (
            <Typography level="body-sm" color="danger" sx={{ mt: '8px' }}>
              {error}
            </Typography>
          )}
          {images.length > 0 && (
            <Box>
              <Typography level="body-sm" sx={{ mb: 1 }}>
                {t.ai.pick_an_image}
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(4, 1fr)',
                  gap: 1,
                }}
              >
                {images.map((image, index) => (
                  <Box
                    key={index}
                    onClick={() => handleImageSelect(index)}
                    sx={{
                      cursor: 'pointer',
                      border: selectedIndex === index ? '2px solid' : '2px solid transparent',
                      borderColor: selectedIndex === index ? 'primary.500' : 'transparent',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      transition: 'border-color 0.2s',
                      '&:hover': {
                        borderColor: 'primary.300',
                      },
                    }}
                  >
                    <AspectRatio ratio="1">
                      <img
                        src={relativePathDisplay(image.relativePath, ctx.storageHostname)}
                        alt={`Generated image ${index + 1}`}
                        style={{
                          objectFit: 'cover',
                        }}
                      />
                    </AspectRatio>
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
}
