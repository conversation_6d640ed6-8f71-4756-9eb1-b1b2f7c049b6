import { useGlobalContext } from '@bika/types/website/context';

/**
 * Hook to build file preview URLs using the global context
 * This hook accesses the kkFilePreviewUrl from the servers configuration
 */

export const useFilePreview = () => {
  const globalContext = useGlobalContext();

  const buildPreviewUrl = (attachmentUrl: string): string => {
    const fileViewApiUrl = globalContext?.servers?.kkFilePreviewUrl ?? 'https://fileview-dev.bika.ai/onlinePreview';
    const apiUrl = fileViewApiUrl || 'https://fileview-dev.bika.ai/onlinePreview';
    const url = encodeURIComponent(Buffer.from(attachmentUrl, 'utf-8').toString('base64'));
    return `${apiUrl}?url=${url}`;
  };

  return {
    buildPreviewUrl,
  };
};
